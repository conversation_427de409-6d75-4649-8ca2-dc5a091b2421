package com.tcl.ai.note.handwritingtext.richtext.accessibility;

import android.content.Context;
import android.graphics.Rect;
import android.text.Layout;
import android.text.Spanned;
import android.view.MotionEvent;
import android.view.accessibility.AccessibilityEvent;

import com.tcl.ai.note.handwritingtext.richtext.spans.UpcomingListSpan;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Upcoming;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;

import java.util.ArrayList;
import java.util.List;

/**
 * Simplified accessibility helper for UpcomingListSpan in AREditText
 * Handles hover events and double tap to toggle todo items
 */
public class SimpleUpcomingSpanAccessibilityHelper {
    private static final String TAG = "SimpleUpcomingSpanAccessibilityHelper";
    
    private final AREditText mEditText;
    private final List<UpcomingSpanInfo> mUpcomingSpans = new ArrayList<>();
    private boolean mTouchExplorationEnabled = false;
    
    /**
     * Todo item span information
     */
    private static class UpcomingSpanInfo {
        UpcomingListSpan span;
        int start;
        int end;
        int lineNumber;
        Rect bounds;
        
        UpcomingSpanInfo(UpcomingListSpan span, int start, int end, int lineNumber) {
            this.span = span;
            this.start = start;
            this.end = end;
            this.lineNumber = lineNumber;
            this.bounds = new Rect();
        }
    }
    
    public SimpleUpcomingSpanAccessibilityHelper(AREditText editText) {
        mEditText = editText;
        checkTouchExplorationEnabled();
    }
    
    /**
     * Check if touch exploration (TalkBack) is enabled
     */
    private void checkTouchExplorationEnabled() {
        try {
            android.view.accessibility.AccessibilityManager am = 
                (android.view.accessibility.AccessibilityManager) mEditText.getContext().getSystemService(Context.ACCESSIBILITY_SERVICE);
            mTouchExplorationEnabled = am != null && am.isEnabled() && am.isTouchExplorationEnabled();
            android.util.Log.d(TAG, "Touch exploration enabled: " + mTouchExplorationEnabled);
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to check touch exploration", e);
            mTouchExplorationEnabled = false;
        }
    }
    
    /**
     * Handle hover events for accessibility
     */
    public boolean dispatchHoverEvent(MotionEvent event) {
        if (!mTouchExplorationEnabled) {
            return false;
        }
        
        refreshUpcomingSpans();
        
        if (mUpcomingSpans.isEmpty()) {
            return false;
        }
        
        int action = event.getAction();
        if (action == MotionEvent.ACTION_HOVER_ENTER || action == MotionEvent.ACTION_HOVER_MOVE) {
            return handleHoverEvent(event);
        }
        
        return false;
    }
    
    /**
     * Handle hover event to find and announce todo items
     */
    private boolean handleHoverEvent(MotionEvent event) {
        float x = event.getX();
        float y = event.getY();
        
        for (int i = 0; i < mUpcomingSpans.size(); i++) {
            UpcomingSpanInfo info = mUpcomingSpans.get(i);
            if (info.bounds.contains((int) x, (int) y)) {
                announceUpcomingSpan(info, i);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Announce the todo item for accessibility
     */
    private void announceUpcomingSpan(UpcomingSpanInfo info, int index) {
        try {
            String lineText = getLineText(info);
            String statusText = info.span.isChecked() ? "completed" : "uncompleted";
            String announcement = statusText + " todo item " + (index + 1) + ": " + lineText + ". Double tap to toggle.";
            
            // Send accessibility event
            AccessibilityEvent event = AccessibilityEvent.obtain(AccessibilityEvent.TYPE_VIEW_HOVER_ENTER);
            event.setSource(mEditText);
            event.setClassName("android.widget.CheckBox");
            event.getText().add(announcement);
            event.setContentDescription(announcement);
            
            android.view.ViewParent parent = mEditText.getParent();
            if (parent != null) {
                parent.requestSendAccessibilityEvent(mEditText, event);
            }
            
            android.util.Log.d(TAG, "Announced: " + announcement);
            
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to announce span", e);
        }
    }
    
    /**
     * Handle double tap events for accessibility
     */
    public boolean handleDoubleTap(MotionEvent event) {
        if (!mTouchExplorationEnabled) {
            return false;
        }
        
        refreshUpcomingSpans();
        
        if (mUpcomingSpans.isEmpty()) {
            return false;
        }
        
        float x = event.getX();
        float y = event.getY();
        
        for (int i = 0; i < mUpcomingSpans.size(); i++) {
            UpcomingSpanInfo info = mUpcomingSpans.get(i);
            if (info.bounds.contains((int) x, (int) y)) {
                toggleUpcomingSpan(info, i);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Toggle the todo item status
     */
    private void toggleUpcomingSpan(UpcomingSpanInfo info, int index) {
        try {
            // Toggle status
            boolean newStatus = !info.span.isChecked();
            info.span.setChecked(newStatus);
            
            // Update style
            ARE_Upcoming upcoming = new ARE_Upcoming(mEditText.getContext());
            upcoming.setEditText(mEditText);
            upcoming.toggleStrikeboundSpan(newStatus, info.span);
            
            // Trigger content change notification
            Runnable saveTask = mEditText.getSaveContentToMemoryTask();
            if (saveTask != null) {
                saveTask.run();
            }
            
            // Announce the change
            String lineText = getLineText(info);
            String statusText = newStatus ? "completed" : "uncompleted";
            String announcement = "Todo item " + (index + 1) + " marked as " + statusText + ": " + lineText;
            
            AccessibilityEvent event = AccessibilityEvent.obtain(AccessibilityEvent.TYPE_VIEW_CLICKED);
            event.setSource(mEditText);
            event.setClassName("android.widget.CheckBox");
            event.getText().add(announcement);
            event.setContentDescription(announcement);
            
            android.view.ViewParent parent = mEditText.getParent();
            if (parent != null) {
                parent.requestSendAccessibilityEvent(mEditText, event);
            }
            
            android.util.Log.d(TAG, "Toggled span " + index + " to " + statusText);
            
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to toggle span", e);
        }
    }
    
    /**
     * Check if there are any upcoming spans
     */
    public boolean hasUpcomingSpans() {
        refreshUpcomingSpans();
        return !mUpcomingSpans.isEmpty();
    }
    
    /**
     * Get the count of upcoming spans
     */
    public int getUpcomingSpansCount() {
        refreshUpcomingSpans();
        return mUpcomingSpans.size();
    }
    
    /**
     * Refresh todo items list when text content changes
     */
    public void refreshUpcomingSpans() {
        mUpcomingSpans.clear();
        
        CharSequence text = mEditText.getText();
        if (text == null || !(text instanceof Spanned)) {
            return;
        }
        
        Spanned spanned = (Spanned) text;
        UpcomingListSpan[] spans = spanned.getSpans(0, spanned.length(), UpcomingListSpan.class);
        
        Layout layout = mEditText.getLayout();
        if (layout == null) {
            return;
        }
        
        for (UpcomingListSpan span : spans) {
            int start = spanned.getSpanStart(span);
            int end = spanned.getSpanEnd(span);
            int lineNumber = layout.getLineForOffset(start);
            
            UpcomingSpanInfo info = new UpcomingSpanInfo(span, start, end, lineNumber);
            calculateSpanBounds(info, layout);
            mUpcomingSpans.add(info);
        }
        
        android.util.Log.d(TAG, "refreshUpcomingSpans: found " + mUpcomingSpans.size() + " upcoming spans");
    }
    
    /**
     * Calculate bounds for todo item
     */
    private void calculateSpanBounds(UpcomingSpanInfo info, Layout layout) {
        int lineTop = layout.getLineTop(info.lineNumber);
        int lineBottom = layout.getLineBottom(info.lineNumber);
        
        // Set bounds to cover the entire line for easier touch targeting
        info.bounds.left = mEditText.getPaddingLeft();
        info.bounds.top = lineTop + mEditText.getPaddingTop();
        info.bounds.right = mEditText.getWidth() - mEditText.getPaddingRight();
        info.bounds.bottom = lineBottom + mEditText.getPaddingTop();
        
        // Ensure bounds are within EditText range
        info.bounds.left = Math.max(0, info.bounds.left);
        info.bounds.right = Math.min(mEditText.getWidth(), info.bounds.right);
        info.bounds.top = Math.max(0, info.bounds.top);
        info.bounds.bottom = Math.min(mEditText.getHeight(), info.bounds.bottom);
        
        // Ensure minimum height for touch targeting
        if (info.bounds.bottom - info.bounds.top < 48) { // 48dp minimum touch target
            int center = (info.bounds.top + info.bounds.bottom) / 2;
            info.bounds.top = center - 24;
            info.bounds.bottom = center + 24;
        }
        
        android.util.Log.d(TAG, "calculateSpanBounds for line " + info.lineNumber +
            ": bounds=" + info.bounds + ", editText size=" + mEditText.getWidth() + "x" + mEditText.getHeight());
    }
    
    /**
     * Get text content of the line containing the todo item
     */
    private String getLineText(UpcomingSpanInfo info) {
        CharSequence text = mEditText.getText();
        if (text == null) {
            return "";
        }
        
        Layout layout = mEditText.getLayout();
        if (layout == null) {
            return "";
        }
        
        int lineStart = layout.getLineStart(info.lineNumber);
        int lineEnd = layout.getLineEnd(info.lineNumber);
        
        // Remove newline character
        if (lineEnd > lineStart && text.charAt(lineEnd - 1) == '\n') {
            lineEnd--;
        }

        String lineText = text.subSequence(lineStart, lineEnd).toString().trim();
        
        // Remove possible zero-width characters
        lineText = lineText.replace("\u200B", "");
        
        return lineText.isEmpty() ? "empty todo item" : lineText;
    }
}

package com.tcl.ai.note.handwritingtext.richtext.accessibility;

import android.graphics.Rect;
import android.os.Bundle;
import android.text.Layout;
import android.text.Spanned;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import android.view.accessibility.AccessibilityNodeProvider;

import com.tcl.ai.note.handwritingtext.richtext.spans.UpcomingListSpan;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Upcoming;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;

import java.util.ArrayList;
import java.util.List;

/**
 * Accessibility helper class for UpcomingListSpan in AREditText
 * Creates virtual accessibility nodes for TalkBack to focus and interact with todo items
 */
public class UpcomingSpanAccessibilityHelper {
    private static final String TAG = "UpcomingSpanAccessibilityHelper";
    private static final int HOST_VIEW_ID = -1;

    private final AREditText mEditText;
    private final List<UpcomingSpanInfo> mUpcomingSpans = new ArrayList<>();
    private AccessibilityNodeProvider mNodeProvider;

    /**
     * Todo item span information
     */
    private static class UpcomingSpanInfo {
        UpcomingListSpan span;
        int start;
        int end;
        int lineNumber;
        Rect bounds;

        UpcomingSpanInfo(UpcomingListSpan span, int start, int end, int lineNumber) {
            this.span = span;
            this.start = start;
            this.end = end;
            this.lineNumber = lineNumber;
            this.bounds = new Rect();
        }
    }

    public UpcomingSpanAccessibilityHelper(AREditText editText) {
        mEditText = editText;
        mNodeProvider = new UpcomingSpanNodeProvider();
    }

    /**
     * Get the AccessibilityNodeProvider instance
     */
    public AccessibilityNodeProvider getAccessibilityNodeProvider() {
        return mNodeProvider;
    }

    /**
     * Check if there are any upcoming spans
     */
    public boolean hasUpcomingSpans() {
        return !mUpcomingSpans.isEmpty();
    }

    /**
     * Get the count of upcoming spans
     */
    public int getUpcomingSpansCount() {
        return mUpcomingSpans.size();
    }

    /**
     * Custom AccessibilityNodeProvider for handling todo items
     */
    private class UpcomingSpanNodeProvider extends AccessibilityNodeProvider {

        @Override
        public AccessibilityNodeInfo createAccessibilityNodeInfo(int virtualViewId) {
            if (virtualViewId == HOST_VIEW_ID) {
                // Return info for the host view (AREditText)
                AccessibilityNodeInfo info = AccessibilityNodeInfo.obtain();

                // 手动设置主要属性而不是调用onInitializeAccessibilityNodeInfo
                info.setSource(mEditText);
                info.setClassName(mEditText.getClass().getName());
                info.setPackageName(mEditText.getContext().getPackageName());
                info.setFocusable(true);
                info.setEditable(true);
                info.setClickable(true);
                info.setEnabled(true);
                info.setVisibleToUser(true);

                // 设置边界
                android.graphics.Rect bounds = new android.graphics.Rect();
                mEditText.getBoundsOnScreen(bounds);
                info.setBoundsInScreen(bounds);

                // 设置内容描述
                StringBuilder contentDesc = new StringBuilder();
                contentDesc.append("Rich text editor with ").append(mUpcomingSpans.size()).append(" todo items");
                info.setContentDescription(contentDesc.toString());

                // Add virtual children
                for (int i = 0; i < mUpcomingSpans.size(); i++) {
                    info.addChild(mEditText, i);
                }

                return info;
            }

            // Return info for virtual child (todo item)
            if (virtualViewId >= 0 && virtualViewId < mUpcomingSpans.size()) {
                UpcomingSpanInfo spanInfo = mUpcomingSpans.get(virtualViewId);
                AccessibilityNodeInfo info = AccessibilityNodeInfo.obtain();

                info.setClassName("android.widget.CheckBox");
                info.setPackageName(mEditText.getContext().getPackageName());
                info.setSource(mEditText, virtualViewId);
                info.setParent(mEditText);

                info.setCheckable(true);
                info.setChecked(spanInfo.span.isChecked());
                info.setClickable(true);
                info.setFocusable(true);
                info.setEnabled(true);
                info.setVisibleToUser(true);

                // Set content description
                String lineText = getLineText(spanInfo);
                String statusText = spanInfo.span.isChecked() ? "completed" : "uncompleted";
                String contentDesc = statusText + " todo item: " + lineText;
                info.setContentDescription(contentDesc);

                // Set bounds in parent and screen
                info.setBoundsInParent(spanInfo.bounds);

                // Calculate bounds in screen coordinates
                Rect screenBounds = new Rect(spanInfo.bounds);
                int[] location = new int[2];
                mEditText.getLocationOnScreen(location);
                screenBounds.offset(location[0], location[1]);
                info.setBoundsInScreen(screenBounds);

                // Add click action
                info.addAction(AccessibilityNodeInfo.ACTION_CLICK);

                android.util.Log.d(TAG, "Created virtual node " + virtualViewId +
                    ": " + contentDesc + ", bounds=" + spanInfo.bounds);

                return info;
            }

            return null;
        }

        @Override
        public boolean performAction(int virtualViewId, int action, Bundle arguments) {
            android.util.Log.d(TAG, "performAction: virtualViewId=" + virtualViewId + ", action=" + action);

            if (virtualViewId >= 0 && virtualViewId < mUpcomingSpans.size() &&
                action == AccessibilityNodeInfo.ACTION_CLICK) {

                UpcomingSpanInfo info = mUpcomingSpans.get(virtualViewId);

                android.util.Log.d(TAG, "Clicking todo item " + virtualViewId +
                    ", current state: " + info.span.isChecked());

                // Toggle todo item status
                info.span.setChecked(!info.span.isChecked());

                // Trigger style update
                ARE_Upcoming upcoming = new ARE_Upcoming(mEditText.getContext());
                upcoming.setEditText(mEditText);
                upcoming.toggleStrikeboundSpan(info.span.isChecked(), info.span);

                // Trigger content change notification
                Runnable saveTask = mEditText.getSaveContentToMemoryTask();
                if (saveTask != null) {
                    saveTask.run();
                }

                // Send accessibility event
                AccessibilityEvent event = AccessibilityEvent.obtain(AccessibilityEvent.TYPE_VIEW_CLICKED);
                event.setSource(mEditText, virtualViewId);
                event.setClassName("android.widget.CheckBox");

                String lineText = getLineText(info);
                String statusText = info.span.isChecked() ? "completed" : "uncompleted";
                event.setContentDescription(statusText + " todo item: " + lineText);

                mEditText.getParent().requestSendAccessibilityEvent(mEditText, event);

                android.util.Log.d(TAG, "Todo item " + virtualViewId + " clicked, new state: " + info.span.isChecked());

                return true;
            }

            return false;
        }
    }

    /**
     * Refresh todo items list when text content changes
     */
    public void refreshUpcomingSpans() {
        boolean hadSpans = !mUpcomingSpans.isEmpty();
        mUpcomingSpans.clear();

        if (mEditText.getText() == null) {
            if (hadSpans) {
                // Notify that content structure changed
                sendAccessibilityEvent(android.view.accessibility.AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED);
            }
            return;
        }

        Spanned spanned = mEditText.getText();
        UpcomingListSpan[] spans = spanned.getSpans(0, spanned.length(), UpcomingListSpan.class);

        Layout layout = mEditText.getLayout();
        if (layout == null) {
            if (hadSpans) {
                // Notify that content structure changed
                sendAccessibilityEvent(android.view.accessibility.AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED);
            }
            return;
        }

        for (UpcomingListSpan span : spans) {
            int start = spanned.getSpanStart(span);
            int end = spanned.getSpanEnd(span);
            int lineNumber = layout.getLineForOffset(start);

            UpcomingSpanInfo info = new UpcomingSpanInfo(span, start, end, lineNumber);
            calculateSpanBounds(info, layout);
            mUpcomingSpans.add(info);
        }

        // Log for debugging
        android.util.Log.d(TAG, "refreshUpcomingSpans: found " + mUpcomingSpans.size() + " upcoming spans");

        // Notify accessibility service that content structure changed
        if (hadSpans || !mUpcomingSpans.isEmpty()) {
            sendAccessibilityEvent(android.view.accessibility.AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED);
        }
    }

    /**
     * Send accessibility event
     */
    private void sendAccessibilityEvent(int eventType) {
        try {
            android.view.accessibility.AccessibilityEvent event =
                android.view.accessibility.AccessibilityEvent.obtain(eventType);
            event.setSource(mEditText);
            event.setClassName(mEditText.getClass().getName());
            event.setPackageName(mEditText.getContext().getPackageName());

            if (mEditText.getParent() != null) {
                mEditText.getParent().requestSendAccessibilityEvent(mEditText, event);
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to send accessibility event", e);
        }
    }
    
    /**
     * Calculate bounds for todo item
     */
    private void calculateSpanBounds(UpcomingSpanInfo info, Layout layout) {
        int lineTop = layout.getLineTop(info.lineNumber);
        int lineBottom = layout.getLineBottom(info.lineNumber);

        // Get line start and end positions
        int lineStart = layout.getLineStart(info.lineNumber);
        int lineEnd = layout.getLineEnd(info.lineNumber);

        // Calculate todo item area - make it cover the entire line for better accessibility
        float lineLeft = layout.getLineLeft(info.lineNumber);
        float lineRight = layout.getLineRight(info.lineNumber);

        // Set bounds to cover the entire line for easier touch targeting
        info.bounds.left = mEditText.getPaddingLeft();
        info.bounds.top = lineTop + mEditText.getPaddingTop();
        info.bounds.right = mEditText.getWidth() - mEditText.getPaddingRight();
        info.bounds.bottom = lineBottom + mEditText.getPaddingTop();

        // Ensure bounds are within EditText range
        info.bounds.left = Math.max(0, info.bounds.left);
        info.bounds.right = Math.min(mEditText.getWidth(), info.bounds.right);
        info.bounds.top = Math.max(0, info.bounds.top);
        info.bounds.bottom = Math.min(mEditText.getHeight(), info.bounds.bottom);

        // 确保边界有最小高度，便于点击
        if (info.bounds.bottom - info.bounds.top < 48) { // 48dp minimum touch target
            int center = (info.bounds.top + info.bounds.bottom) / 2;
            info.bounds.top = center - 24;
            info.bounds.bottom = center + 24;
        }

        android.util.Log.d(TAG, "calculateSpanBounds for line " + info.lineNumber +
            ": bounds=" + info.bounds + ", editText size=" + mEditText.getWidth() + "x" + mEditText.getHeight());
    }
    
    /**
     * Get text content of the line containing the todo item
     */
    private String getLineText(UpcomingSpanInfo info) {
        if (mEditText.getText() == null) {
            return "";
        }
        
        Layout layout = mEditText.getLayout();
        if (layout == null) {
            return "";
        }
        
        int lineStart = layout.getLineStart(info.lineNumber);
        int lineEnd = layout.getLineEnd(info.lineNumber);
        
        // Remove newline character
        if (lineEnd > lineStart && mEditText.getText().charAt(lineEnd - 1) == '\n') {
            lineEnd--;
        }

        String lineText = mEditText.getText().subSequence(lineStart, lineEnd).toString().trim();

        // Remove possible zero-width characters
        lineText = lineText.replace("\u200B", "");

        return lineText.isEmpty() ? "empty todo item" : lineText;
    }
}

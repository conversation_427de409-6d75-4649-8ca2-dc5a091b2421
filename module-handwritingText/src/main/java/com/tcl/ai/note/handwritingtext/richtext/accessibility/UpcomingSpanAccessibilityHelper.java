package com.tcl.ai.note.handwritingtext.richtext.accessibility;

import android.content.Context;
import android.graphics.Rect;
import android.os.Bundle;
import android.text.Layout;
import android.text.Spanned;
import android.view.MotionEvent;
import android.view.View;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import android.view.accessibility.AccessibilityNodeProvider;

import com.tcl.ai.note.handwritingtext.richtext.spans.UpcomingListSpan;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Upcoming;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * Accessibility helper class for UpcomingListSpan in AREditText
 * Creates virtual accessibility nodes for TalkBack to focus and interact with todo items
 * Uses reflection to avoid type casting issues
 */
public class UpcomingSpanAccessibilityHelper {
    private static final String TAG = "UpcomingSpanAccessibilityHelper";
    private static final int HOST_VIEW_ID = -1;

    private final AREditText mEditText;
    private final List<UpcomingSpanInfo> mUpcomingSpans = new ArrayList<>();
    private AccessibilityNodeProvider mNodeProvider;

    /**
     * Todo item span information
     */
    private static class UpcomingSpanInfo {
        UpcomingListSpan span;
        int start;
        int end;
        int lineNumber;
        Rect bounds;

        UpcomingSpanInfo(UpcomingListSpan span, int start, int end, int lineNumber) {
            this.span = span;
            this.start = start;
            this.end = end;
            this.lineNumber = lineNumber;
            this.bounds = new Rect();
        }
    }

    public UpcomingSpanAccessibilityHelper(AREditText editText) {
        mEditText = editText;
        mNodeProvider = new UpcomingSpanNodeProvider();
    }

    /**
     * Get the AccessibilityNodeProvider instance
     */
    public AccessibilityNodeProvider getAccessibilityNodeProvider() {
        return mNodeProvider;
    }

    /**
     * Handle hover events for accessibility
     */
    public boolean dispatchHoverEvent(MotionEvent event) {
        try {
            Method method = mNodeProvider.getClass().getMethod("dispatchHoverEvent", MotionEvent.class);
            return (Boolean) method.invoke(mNodeProvider, event);
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to dispatch hover event", e);
            return false;
        }
    }

    // 反射工具方法，避免类型转换问题
    private Context getEditTextContext() {
        try {
            Method method = mEditText.getClass().getMethod("getContext");
            return (Context) method.invoke(mEditText);
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to get context", e);
            return null;
        }
    }

    private int getEditTextWidth() {
        try {
            Method method = mEditText.getClass().getMethod("getWidth");
            return (Integer) method.invoke(mEditText);
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to get width", e);
            return 0;
        }
    }

    private int getEditTextHeight() {
        try {
            Method method = mEditText.getClass().getMethod("getHeight");
            return (Integer) method.invoke(mEditText);
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to get height", e);
            return 0;
        }
    }

    private int getEditTextPaddingLeft() {
        try {
            Method method = mEditText.getClass().getMethod("getPaddingLeft");
            return (Integer) method.invoke(mEditText);
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to get padding left", e);
            return 0;
        }
    }

    private int getEditTextPaddingTop() {
        try {
            Method method = mEditText.getClass().getMethod("getPaddingTop");
            return (Integer) method.invoke(mEditText);
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to get padding top", e);
            return 0;
        }
    }

    private int getEditTextPaddingRight() {
        try {
            Method method = mEditText.getClass().getMethod("getPaddingRight");
            return (Integer) method.invoke(mEditText);
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to get padding right", e);
            return 0;
        }
    }

    private void getEditTextLocationOnScreen(int[] location) {
        try {
            Method method = mEditText.getClass().getMethod("getLocationOnScreen", int[].class);
            method.invoke(mEditText, location);
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to get location on screen", e);
            location[0] = 0;
            location[1] = 0;
        }
    }

    private android.view.ViewParent getEditTextParent() {
        try {
            Method method = mEditText.getClass().getMethod("getParent");
            return (android.view.ViewParent) method.invoke(mEditText);
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to get parent", e);
            return null;
        }
    }

    private CharSequence getEditTextText() {
        try {
            Method method = mEditText.getClass().getMethod("getText");
            return (CharSequence) method.invoke(mEditText);
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to get text", e);
            return null;
        }
    }

    private Layout getEditTextLayout() {
        try {
            Method method = mEditText.getClass().getMethod("getLayout");
            return (Layout) method.invoke(mEditText);
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to get layout", e);
            return null;
        }
    }

    /**
     * Check if there are any upcoming spans
     */
    public boolean hasUpcomingSpans() {
        return !mUpcomingSpans.isEmpty();
    }

    /**
     * Get the count of upcoming spans
     */
    public int getUpcomingSpansCount() {
        return mUpcomingSpans.size();
    }

    /**
     * Custom AccessibilityNodeProvider for handling todo items
     */
    private class UpcomingSpanNodeProvider extends AccessibilityNodeProvider {

        @Override
        public AccessibilityNodeInfo createAccessibilityNodeInfo(int virtualViewId) {
            if (virtualViewId == HOST_VIEW_ID) {
                // Return info for the host view (AREditText)
                AccessibilityNodeInfo info = AccessibilityNodeInfo.obtain();

                try {
                    // 使用反射设置主要属性
                    Method setSourceMethod = info.getClass().getMethod("setSource", View.class);
                    setSourceMethod.invoke(info, mEditText);

                    info.setClassName(mEditText.getClass().getName());

                    Context context = getEditTextContext();
                    if (context != null) {
                        info.setPackageName(context.getPackageName());
                    }

                    info.setFocusable(true);
                    info.setEditable(true);
                    info.setClickable(true);
                    info.setEnabled(true);
                    info.setVisibleToUser(true);

                    // 设置边界
                    android.graphics.Rect bounds = new android.graphics.Rect();
                    int[] location = new int[2];
                    getEditTextLocationOnScreen(location);
                    bounds.left = location[0];
                    bounds.top = location[1];
                    bounds.right = location[0] + getEditTextWidth();
                    bounds.bottom = location[1] + getEditTextHeight();
                    info.setBoundsInScreen(bounds);

                    // 设置内容描述
                    StringBuilder contentDesc = new StringBuilder();
                    contentDesc.append("Rich text editor with ").append(mUpcomingSpans.size()).append(" todo items");
                    info.setContentDescription(contentDesc.toString());

                    // Add virtual children
                    Method addChildMethod = info.getClass().getMethod("addChild", View.class, int.class);
                    for (int i = 0; i < mUpcomingSpans.size(); i++) {
                        addChildMethod.invoke(info, mEditText, i);
                    }

                } catch (Exception e) {
                    android.util.Log.e(TAG, "Failed to create host node info", e);
                }

                return info;
            }

            // Return info for virtual child (todo item)
            if (virtualViewId >= 0 && virtualViewId < mUpcomingSpans.size()) {
                UpcomingSpanInfo spanInfo = mUpcomingSpans.get(virtualViewId);
                AccessibilityNodeInfo info = AccessibilityNodeInfo.obtain();

                try {
                    info.setClassName("android.widget.CheckBox");

                    Context context = getEditTextContext();
                    if (context != null) {
                        info.setPackageName(context.getPackageName());
                    }

                    // 使用反射设置source和parent
                    Method setSourceMethod = info.getClass().getMethod("setSource", View.class, int.class);
                    setSourceMethod.invoke(info, mEditText, virtualViewId);

                    Method setParentMethod = info.getClass().getMethod("setParent", View.class);
                    setParentMethod.invoke(info, mEditText);

                    info.setCheckable(true);
                    info.setChecked(spanInfo.span.isChecked());
                    info.setClickable(true);
                    info.setFocusable(true);
                    info.setEnabled(true);
                    info.setVisibleToUser(true);

                    // Set content description
                    String lineText = getLineText(spanInfo);
                    String statusText = spanInfo.span.isChecked() ? "completed" : "uncompleted";
                    String contentDesc = statusText + " todo item: " + lineText;
                    info.setContentDescription(contentDesc);

                    // Set bounds in parent and screen
                    info.setBoundsInParent(spanInfo.bounds);

                    // Calculate bounds in screen coordinates
                    Rect screenBounds = new Rect(spanInfo.bounds);
                    int[] location = new int[2];
                    getEditTextLocationOnScreen(location);
                    screenBounds.offset(location[0], location[1]);
                    info.setBoundsInScreen(screenBounds);

                    // Add click action
                    info.addAction(AccessibilityNodeInfo.ACTION_CLICK);

                    android.util.Log.d(TAG, "Created virtual node " + virtualViewId +
                        ": " + contentDesc + ", bounds=" + spanInfo.bounds);

                } catch (Exception e) {
                    android.util.Log.e(TAG, "Failed to create virtual node " + virtualViewId, e);
                }

                return info;
            }

            return null;
        }

        @Override
        public boolean performAction(int virtualViewId, int action, Bundle arguments) {
            android.util.Log.d(TAG, "performAction: virtualViewId=" + virtualViewId + ", action=" + action);

            if (virtualViewId >= 0 && virtualViewId < mUpcomingSpans.size() &&
                action == AccessibilityNodeInfo.ACTION_CLICK) {

                UpcomingSpanInfo info = mUpcomingSpans.get(virtualViewId);

                android.util.Log.d(TAG, "Clicking todo item " + virtualViewId +
                    ", current state: " + info.span.isChecked());

                try {
                    // Toggle todo item status
                    info.span.setChecked(!info.span.isChecked());

                    // Trigger style update
                    Context context = getEditTextContext();
                    if (context != null) {
                        ARE_Upcoming upcoming = new ARE_Upcoming(context);
                        upcoming.setEditText(mEditText);
                        upcoming.toggleStrikeboundSpan(info.span.isChecked(), info.span);
                    }

                    // Trigger content change notification
                    Runnable saveTask = mEditText.getSaveContentToMemoryTask();
                    if (saveTask != null) {
                        saveTask.run();
                    }

                    // Send accessibility event
                    AccessibilityEvent event = AccessibilityEvent.obtain(AccessibilityEvent.TYPE_VIEW_CLICKED);

                    // 使用反射设置事件源
                    Method setSourceMethod = event.getClass().getMethod("setSource", View.class, int.class);
                    setSourceMethod.invoke(event, mEditText, virtualViewId);

                    event.setClassName("android.widget.CheckBox");

                    String lineText = getLineText(info);
                    String statusText = info.span.isChecked() ? "completed" : "uncompleted";
                    event.setContentDescription(statusText + " todo item: " + lineText);

                    // 发送事件
                    android.view.ViewParent parent = getEditTextParent();
                    if (parent != null) {
                        Method requestSendMethod = parent.getClass().getMethod("requestSendAccessibilityEvent", View.class, AccessibilityEvent.class);
                        requestSendMethod.invoke(parent, mEditText, event);
                    }

                    android.util.Log.d(TAG, "Todo item " + virtualViewId + " clicked, new state: " + info.span.isChecked());

                } catch (Exception e) {
                    android.util.Log.e(TAG, "Failed to perform click action", e);
                    return false;
                }

                return true;
            }

            return false;
        }
    }

    /**
     * Refresh todo items list when text content changes
     */
    public void refreshUpcomingSpans() {
        boolean hadSpans = !mUpcomingSpans.isEmpty();
        mUpcomingSpans.clear();

        CharSequence text = getEditTextText();
        if (text == null) {
            if (hadSpans) {
                // Notify that content structure changed
                sendAccessibilityEvent(android.view.accessibility.AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED);
            }
            return;
        }

        if (!(text instanceof Spanned)) {
            if (hadSpans) {
                sendAccessibilityEvent(android.view.accessibility.AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED);
            }
            return;
        }

        Spanned spanned = (Spanned) text;
        UpcomingListSpan[] spans = spanned.getSpans(0, spanned.length(), UpcomingListSpan.class);

        Layout layout = getEditTextLayout();
        if (layout == null) {
            if (hadSpans) {
                // Notify that content structure changed
                sendAccessibilityEvent(android.view.accessibility.AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED);
            }
            return;
        }

        for (UpcomingListSpan span : spans) {
            int start = spanned.getSpanStart(span);
            int end = spanned.getSpanEnd(span);
            int lineNumber = layout.getLineForOffset(start);

            UpcomingSpanInfo info = new UpcomingSpanInfo(span, start, end, lineNumber);
            calculateSpanBounds(info, layout);
            mUpcomingSpans.add(info);
        }

        // Log for debugging
        android.util.Log.d(TAG, "refreshUpcomingSpans: found " + mUpcomingSpans.size() + " upcoming spans");

        // Notify accessibility service that content structure changed
        if (hadSpans || !mUpcomingSpans.isEmpty()) {
            sendAccessibilityEvent(android.view.accessibility.AccessibilityEvent.TYPE_WINDOW_CONTENT_CHANGED);
        }
    }

    /**
     * Send accessibility event
     */
    private void sendAccessibilityEvent(int eventType) {
        try {
            android.view.accessibility.AccessibilityEvent event =
                android.view.accessibility.AccessibilityEvent.obtain(eventType);

            // 使用反射设置事件源
            Method setSourceMethod = event.getClass().getMethod("setSource", View.class);
            setSourceMethod.invoke(event, mEditText);

            event.setClassName(mEditText.getClass().getName());

            Context context = getEditTextContext();
            if (context != null) {
                event.setPackageName(context.getPackageName());
            }

            android.view.ViewParent parent = getEditTextParent();
            if (parent != null) {
                Method requestSendMethod = parent.getClass().getMethod("requestSendAccessibilityEvent", View.class, AccessibilityEvent.class);
                requestSendMethod.invoke(parent, mEditText, event);
            }
        } catch (Exception e) {
            android.util.Log.e(TAG, "Failed to send accessibility event", e);
        }
    }
    
    /**
     * Calculate bounds for todo item
     */
    private void calculateSpanBounds(UpcomingSpanInfo info, Layout layout) {
        int lineTop = layout.getLineTop(info.lineNumber);
        int lineBottom = layout.getLineBottom(info.lineNumber);

        // Get line start and end positions
        int lineStart = layout.getLineStart(info.lineNumber);
        int lineEnd = layout.getLineEnd(info.lineNumber);

        // Calculate todo item area - make it cover the entire line for better accessibility
        float lineLeft = layout.getLineLeft(info.lineNumber);
        float lineRight = layout.getLineRight(info.lineNumber);

        // Set bounds to cover the entire line for easier touch targeting
        info.bounds.left = getEditTextPaddingLeft();
        info.bounds.top = lineTop + getEditTextPaddingTop();
        info.bounds.right = getEditTextWidth() - getEditTextPaddingRight();
        info.bounds.bottom = lineBottom + getEditTextPaddingTop();

        // Ensure bounds are within EditText range
        info.bounds.left = Math.max(0, info.bounds.left);
        info.bounds.right = Math.min(getEditTextWidth(), info.bounds.right);
        info.bounds.top = Math.max(0, info.bounds.top);
        info.bounds.bottom = Math.min(getEditTextHeight(), info.bounds.bottom);

        // 确保边界有最小高度，便于点击
        if (info.bounds.bottom - info.bounds.top < 48) { // 48dp minimum touch target
            int center = (info.bounds.top + info.bounds.bottom) / 2;
            info.bounds.top = center - 24;
            info.bounds.bottom = center + 24;
        }

        android.util.Log.d(TAG, "calculateSpanBounds for line " + info.lineNumber +
            ": bounds=" + info.bounds + ", editText size=" + getEditTextWidth() + "x" + getEditTextHeight());
    }
    
    /**
     * Get text content of the line containing the todo item
     */
    private String getLineText(UpcomingSpanInfo info) {
        CharSequence text = getEditTextText();
        if (text == null) {
            return "";
        }

        Layout layout = getEditTextLayout();
        if (layout == null) {
            return "";
        }

        int lineStart = layout.getLineStart(info.lineNumber);
        int lineEnd = layout.getLineEnd(info.lineNumber);

        // Remove newline character
        if (lineEnd > lineStart && text.charAt(lineEnd - 1) == '\n') {
            lineEnd--;
        }

        String lineText = text.subSequence(lineStart, lineEnd).toString().trim();

        // Remove possible zero-width characters
        lineText = lineText.replace("\u200B", "");

        return lineText.isEmpty() ? "empty todo item" : lineText;
    }
}

package com.tcl.ai.note.handwritingtext.richtext.accessibility;

import android.graphics.Rect;
import android.os.Bundle;
import android.text.Layout;
import android.text.Spanned;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import android.view.accessibility.AccessibilityNodeProvider;

import com.tcl.ai.note.handwritingtext.richtext.spans.UpcomingListSpan;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Upcoming;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;

import java.util.ArrayList;
import java.util.List;

/**
 * Accessibility helper class for UpcomingListSpan in AREditText
 * Creates virtual accessibility nodes for TalkBack to focus and interact with todo items
 */
public class UpcomingSpanAccessibilityHelper {
    private static final String TAG = "UpcomingSpanAccessibilityHelper";
    private static final int HOST_VIEW_ID = -1;

    private final AREditText mEditText;
    private final List<UpcomingSpanInfo> mUpcomingSpans = new ArrayList<>();
    private AccessibilityNodeProvider mNodeProvider;

    /**
     * Todo item span information
     */
    private static class UpcomingSpanInfo {
        UpcomingListSpan span;
        int start;
        int end;
        int lineNumber;
        Rect bounds;

        UpcomingSpanInfo(UpcomingListSpan span, int start, int end, int lineNumber) {
            this.span = span;
            this.start = start;
            this.end = end;
            this.lineNumber = lineNumber;
            this.bounds = new Rect();
        }
    }

    public UpcomingSpanAccessibilityHelper(AREditText editText) {
        mEditText = editText;
        mNodeProvider = new UpcomingSpanNodeProvider();
    }

    /**
     * Get the AccessibilityNodeProvider instance
     */
    public AccessibilityNodeProvider getAccessibilityNodeProvider() {
        return mNodeProvider;
    }

    /**
     * Custom AccessibilityNodeProvider for handling todo items
     */
    private class UpcomingSpanNodeProvider extends AccessibilityNodeProvider {

        @Override
        public AccessibilityNodeInfo createAccessibilityNodeInfo(int virtualViewId) {
            if (virtualViewId == HOST_VIEW_ID) {
                // Return info for the host view (AREditText)
                AccessibilityNodeInfo info = AccessibilityNodeInfo.obtain(mEditText);
                mEditText.onInitializeAccessibilityNodeInfo(info);

                // Add virtual children
                for (int i = 0; i < mUpcomingSpans.size(); i++) {
                    info.addChild(mEditText, i);
                }

                return info;
            }

            // Return info for virtual child (todo item)
            if (virtualViewId >= 0 && virtualViewId < mUpcomingSpans.size()) {
                UpcomingSpanInfo spanInfo = mUpcomingSpans.get(virtualViewId);
                AccessibilityNodeInfo info = AccessibilityNodeInfo.obtain();

                info.setClassName("android.widget.CheckBox");
                info.setPackageName(mEditText.getContext().getPackageName());
                info.setSource(mEditText, virtualViewId);
                info.setParent(mEditText);

                info.setCheckable(true);
                info.setChecked(spanInfo.span.isChecked());
                info.setClickable(true);
                info.setFocusable(true);
                info.setEnabled(true);

                // Set content description
                String lineText = getLineText(spanInfo);
                String statusText = spanInfo.span.isChecked() ? "completed" : "uncompleted";
                String contentDesc = statusText + " todo item: " + lineText;
                info.setContentDescription(contentDesc);

                // Set bounds
                info.setBoundsInParent(spanInfo.bounds);

                // Add click action
                info.addAction(AccessibilityNodeInfo.ACTION_CLICK);

                return info;
            }

            return null;
        }

        @Override
        public boolean performAction(int virtualViewId, int action, Bundle arguments) {
            if (virtualViewId >= 0 && virtualViewId < mUpcomingSpans.size() &&
                action == AccessibilityNodeInfo.ACTION_CLICK) {

                UpcomingSpanInfo info = mUpcomingSpans.get(virtualViewId);

                // Toggle todo item status
                info.span.setChecked(!info.span.isChecked());

                // Trigger style update
                ARE_Upcoming upcoming = new ARE_Upcoming(mEditText.getContext());
                upcoming.setEditText(mEditText);
                upcoming.toggleStrikeboundSpan(info.span.isChecked(), info.span);

                // Trigger content change notification
                Runnable saveTask = mEditText.getSaveContentToMemoryTask();
                if (saveTask != null) {
                    saveTask.run();
                }

                // Send accessibility event
                AccessibilityEvent event = AccessibilityEvent.obtain(AccessibilityEvent.TYPE_VIEW_CLICKED);
                event.setSource(mEditText, virtualViewId);
                event.setClassName("android.widget.CheckBox");

                String lineText = getLineText(info);
                String statusText = info.span.isChecked() ? "completed" : "uncompleted";
                event.setContentDescription(statusText + " todo item: " + lineText);

                mEditText.getParent().requestSendAccessibilityEvent(mEditText, event);

                return true;
            }

            return false;
        }
    }

    /**
     * Refresh todo items list when text content changes
     */
    public void refreshUpcomingSpans() {
        mUpcomingSpans.clear();

        if (mEditText.getText() == null) {
            return;
        }

        Spanned spanned = mEditText.getText();
        UpcomingListSpan[] spans = spanned.getSpans(0, spanned.length(), UpcomingListSpan.class);

        Layout layout = mEditText.getLayout();
        if (layout == null) {
            return;
        }

        for (UpcomingListSpan span : spans) {
            int start = spanned.getSpanStart(span);
            int end = spanned.getSpanEnd(span);
            int lineNumber = layout.getLineForOffset(start);

            UpcomingSpanInfo info = new UpcomingSpanInfo(span, start, end, lineNumber);
            calculateSpanBounds(info, layout);
            mUpcomingSpans.add(info);
        }

        // Log for debugging (remove Logger dependency)
        android.util.Log.d(TAG, "refreshUpcomingSpans: found " + mUpcomingSpans.size() + " upcoming spans");
    }
    
    /**
     * Calculate bounds for todo item
     */
    private void calculateSpanBounds(UpcomingSpanInfo info, Layout layout) {
        int lineTop = layout.getLineTop(info.lineNumber);
        int lineBottom = layout.getLineBottom(info.lineNumber);

        // Get line start and end positions
        int lineStart = layout.getLineStart(info.lineNumber);
        int lineEnd = layout.getLineEnd(info.lineNumber);

        // Calculate todo item icon area (usually at line start)
        float lineLeft = layout.getLineLeft(info.lineNumber);
        float leadingMargin = info.span.getLeadingMargin(true);

        // Set bounds: include icon and part of text area
        info.bounds.left = (int) (lineLeft - leadingMargin);
        info.bounds.top = lineTop + mEditText.getPaddingTop();
        info.bounds.right = (int) (lineLeft + leadingMargin);
        info.bounds.bottom = lineBottom + mEditText.getPaddingTop();

        // Ensure bounds are within EditText range
        info.bounds.left = Math.max(0, info.bounds.left);
        info.bounds.right = Math.min(mEditText.getWidth(), info.bounds.right);
    }
    
    /**
     * Get text content of the line containing the todo item
     */
    private String getLineText(UpcomingSpanInfo info) {
        if (mEditText.getText() == null) {
            return "";
        }
        
        Layout layout = mEditText.getLayout();
        if (layout == null) {
            return "";
        }
        
        int lineStart = layout.getLineStart(info.lineNumber);
        int lineEnd = layout.getLineEnd(info.lineNumber);
        
        // Remove newline character
        if (lineEnd > lineStart && mEditText.getText().charAt(lineEnd - 1) == '\n') {
            lineEnd--;
        }

        String lineText = mEditText.getText().subSequence(lineStart, lineEnd).toString().trim();

        // Remove possible zero-width characters
        lineText = lineText.replace("\u200B", "");

        return lineText.isEmpty() ? "empty todo item" : lineText;
    }
}

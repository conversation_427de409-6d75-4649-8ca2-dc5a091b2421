package com.tcl.ai.note.handwritingtext.richtext.accessibility;

import android.graphics.Rect;
import android.os.Bundle;
import android.text.Layout;
import android.text.Spanned;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.view.accessibility.AccessibilityNodeInfoCompat;
import androidx.customview.widget.ExploreByTouchHelper;

import com.tcl.ai.note.handwritingtext.richtext.spans.UpcomingListSpan;
import com.tcl.ai.note.handwritingtext.richtext.styles.ARE_Upcoming;
import com.tcl.ai.note.handwritingtext.richtext.views.AREditText;
import com.tcl.ai.note.utils.Logger;

import java.util.ArrayList;
import java.util.List;

/**
 * 为AREditText中的UpcomingListSpan提供无障碍支持的辅助类
 * 通过创建虚拟无障碍节点，让TalkBack能够独立聚焦和操作每个待办事项
 */
public class UpcomingSpanAccessibilityHelper extends ExploreByTouchHelper {
    private static final String TAG = "UpcomingSpanAccessibilityHelper";
    
    private final AREditText mEditText;
    private final List<UpcomingSpanInfo> mUpcomingSpans = new ArrayList<>();
    
    /**
     * 待办事项Span信息
     */
    private static class UpcomingSpanInfo {
        UpcomingListSpan span;
        int start;
        int end;
        int lineNumber;
        Rect bounds;
        
        UpcomingSpanInfo(UpcomingListSpan span, int start, int end, int lineNumber) {
            this.span = span;
            this.start = start;
            this.end = end;
            this.lineNumber = lineNumber;
            this.bounds = new Rect();
        }
    }
    
    public UpcomingSpanAccessibilityHelper(AREditText editText) {
        super(editText);
        mEditText = editText;
    }
    
    /**
     * 刷新待办事项列表，当文本内容发生变化时调用
     */
    public void refreshUpcomingSpans() {
        mUpcomingSpans.clear();
        
        if (mEditText.getText() == null) {
            return;
        }
        
        Spanned spanned = mEditText.getText();
        UpcomingListSpan[] spans = spanned.getSpans(0, spanned.length(), UpcomingListSpan.class);
        
        Layout layout = mEditText.getLayout();
        if (layout == null) {
            return;
        }
        
        for (UpcomingListSpan span : spans) {
            int start = spanned.getSpanStart(span);
            int end = spanned.getSpanEnd(span);
            int lineNumber = layout.getLineForOffset(start);
            
            UpcomingSpanInfo info = new UpcomingSpanInfo(span, start, end, lineNumber);
            calculateSpanBounds(info, layout);
            mUpcomingSpans.add(info);
        }
        
        Logger.d(TAG, "refreshUpcomingSpans: found " + mUpcomingSpans.size() + " upcoming spans");
        
        // 通知无障碍服务内容已更改
        invalidateRoot();
    }
    
    /**
     * 计算待办事项的边界区域
     */
    private void calculateSpanBounds(UpcomingSpanInfo info, Layout layout) {
        int lineTop = layout.getLineTop(info.lineNumber);
        int lineBottom = layout.getLineBottom(info.lineNumber);
        
        // 获取行的开始和结束位置
        int lineStart = layout.getLineStart(info.lineNumber);
        int lineEnd = layout.getLineEnd(info.lineNumber);
        
        // 计算待办事项图标的区域（通常在行的开始位置）
        float lineLeft = layout.getLineLeft(info.lineNumber);
        float leadingMargin = info.span.getLeadingMargin(true);
        
        // 设置边界：包含图标和部分文本区域
        info.bounds.left = (int) (lineLeft - leadingMargin);
        info.bounds.top = lineTop + mEditText.getPaddingTop();
        info.bounds.right = (int) (lineLeft + leadingMargin);
        info.bounds.bottom = lineBottom + mEditText.getPaddingTop();
        
        // 确保边界在EditText范围内
        info.bounds.left = Math.max(0, info.bounds.left);
        info.bounds.right = Math.min(mEditText.getWidth(), info.bounds.right);
    }
    
    @Override
    protected int getVirtualViewAt(float x, float y) {
        for (int i = 0; i < mUpcomingSpans.size(); i++) {
            UpcomingSpanInfo info = mUpcomingSpans.get(i);
            if (info.bounds.contains((int) x, (int) y)) {
                Logger.d(TAG, "getVirtualViewAt: found span at index " + i);
                return i;
            }
        }
        return INVALID_ID;
    }
    
    @Override
    protected void getVisibleVirtualViews(List<Integer> virtualViewIds) {
        for (int i = 0; i < mUpcomingSpans.size(); i++) {
            virtualViewIds.add(i);
        }
        Logger.d(TAG, "getVisibleVirtualViews: " + virtualViewIds.size() + " views");
    }
    
    @Override
    protected void onPopulateNodeForVirtualView(int virtualViewId, @NonNull AccessibilityNodeInfoCompat node) {
        if (virtualViewId < 0 || virtualViewId >= mUpcomingSpans.size()) {
            return;
        }
        
        UpcomingSpanInfo info = mUpcomingSpans.get(virtualViewId);
        
        // 设置节点的基本信息
        node.setClassName("android.widget.CheckBox");
        node.setCheckable(true);
        node.setChecked(info.span.isChecked());
        node.setClickable(true);
        node.setFocusable(true);
        
        // 设置内容描述
        String lineText = getLineText(info);
        String statusText = info.span.isChecked() ? "已完成" : "未完成";
        String contentDesc = statusText + "的待办事项：" + lineText;
        node.setContentDescription(contentDesc);
        
        // 设置边界
        node.setBoundsInParent(info.bounds);
        
        // 添加点击动作
        node.addAction(AccessibilityNodeInfoCompat.ACTION_CLICK);
        
        Logger.d(TAG, "onPopulateNodeForVirtualView: " + virtualViewId + " - " + contentDesc);
    }
    
    /**
     * 获取待办事项所在行的文本内容
     */
    private String getLineText(UpcomingSpanInfo info) {
        if (mEditText.getText() == null) {
            return "";
        }
        
        Layout layout = mEditText.getLayout();
        if (layout == null) {
            return "";
        }
        
        int lineStart = layout.getLineStart(info.lineNumber);
        int lineEnd = layout.getLineEnd(info.lineNumber);
        
        // 移除换行符
        if (lineEnd > lineStart && mEditText.getText().charAt(lineEnd - 1) == '\n') {
            lineEnd--;
        }
        
        String lineText = mEditText.getText().subSequence(lineStart, lineEnd).toString().trim();
        
        // 移除可能的零宽字符
        lineText = lineText.replace("\u200B", "");
        
        return lineText.isEmpty() ? "空白待办事项" : lineText;
    }
    
    @Override
    protected boolean onPerformActionForVirtualView(int virtualViewId, int action, @Nullable Bundle arguments) {
        if (virtualViewId < 0 || virtualViewId >= mUpcomingSpans.size()) {
            return false;
        }
        
        if (action == AccessibilityNodeInfoCompat.ACTION_CLICK) {
            UpcomingSpanInfo info = mUpcomingSpans.get(virtualViewId);
            
            Logger.d(TAG, "onPerformActionForVirtualView: clicking span " + virtualViewId);
            
            // 切换待办事项状态
            info.span.setChecked(!info.span.isChecked());
            
            // 触发样式更新
            ARE_Upcoming upcoming = new ARE_Upcoming(mEditText.getContext());
            upcoming.setEditText(mEditText);
            upcoming.toggleStrikeboundSpan(info.span.isChecked(), info.span);
            
            // 触发内容变更通知
            if (mEditText.getSaveContentToMemoryTask() != null) {
                mEditText.getSaveContentToMemoryTask().run();
            }
            
            // 发送无障碍事件
            sendEventForVirtualView(virtualViewId, AccessibilityEvent.TYPE_VIEW_CLICKED);
            
            // 刷新节点信息
            invalidateVirtualView(virtualViewId);
            
            return true;
        }
        
        return false;
    }
    
    @Override
    protected void onPopulateEventForVirtualView(int virtualViewId, @NonNull AccessibilityEvent event) {
        if (virtualViewId < 0 || virtualViewId >= mUpcomingSpans.size()) {
            return;
        }
        
        UpcomingSpanInfo info = mUpcomingSpans.get(virtualViewId);
        String lineText = getLineText(info);
        String statusText = info.span.isChecked() ? "已完成" : "未完成";
        
        event.setContentDescription(statusText + "的待办事项：" + lineText);
        event.setClassName("android.widget.CheckBox");
    }
}
